#!/usr/bin/env python3
import json
import sys

def list_test_names(json_file):
    """
    List all test names in the JSON file.
    """
    try:
        # Load the JSON file
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        # Extract and print test names
        test_names = []
        for item in data.get('summary_items', []):
            test_name = item.get('testName', '')
            if test_name:
                test_names.append(test_name)
        
        # Print the test names
        print(f"Found {len(test_names)} test names in the JSON file:")
        for i, name in enumerate(test_names, 1):
            print(f"{i}. {name}")
        
        # Save to a file for reference
        with open('test_names.txt', 'w') as f:
            for name in test_names:
                f.write(f"{name}\n")
        
        print("\nTest names have been saved to 'test_names.txt'")
        
    except Exception as e:
        print(f"Error: {e}")

def main():
    json_file = 'TruScan_EXECUTIVE HEALTH PACKAGE.json'
    list_test_names(json_file)

if __name__ == "__main__":
    main()
