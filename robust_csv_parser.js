/**
 * Robust CSV parser that can handle malformed CSV files
 * @param {string} csvText - The CSV text to parse
 * @returns {Object} - Object containing headers and data
 */
function parseCSV(csvText) {
    console.log("Parsing CSV data...");
    
    // Clean up the CSV text
    csvText = csvText.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
    
    const lines = csvText.split('\n');
    
    // Skip empty lines at the beginning
    let startLine = 0;
    while (startLine < lines.length && lines[startLine].trim() === '') {
        startLine++;
    }
    
    if (startLine >= lines.length) {
        console.error("CSV file is empty or contains only empty lines");
        return { headers: [], data: {} };
    }
    
    // Extract headers, handling potential issues
    let headerLine = lines[startLine];
    console.log("Header line:", headerLine);
    
    // Handle case where headers might be split across multiple lines
    if (headerLine.includes('\n')) {
        console.log("Headers contain newlines, cleaning up...");
        headerLine = headerLine.replace(/\n/g, ' ');
    }
    
    // Split headers and clean them
    const headers = headerLine.split(',')
        .map(header => header.trim())
        .filter(header => header.length > 0);
    
    console.log("Parsed headers:", headers);
    
    if (headers.length === 0) {
        console.error("No valid headers found in CSV");
        return { headers: [], data: {} };
    }
    
    const data = {};
    
    // Process data rows
    for (let i = startLine + 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;
        
        // Use a more robust CSV parsing approach for values
        const values = parseCSVLine(line);
        
        if (values.length === 0) continue;
        
        const testName = values[0].trim();
        if (!testName) continue;
        
        const rowData = {};
        
        // Map values to headers
        for (let j = 1; j < headers.length && j < values.length; j++) {
            rowData[headers[j]] = values[j];
        }
        
        // If we have more headers than values, fill with empty strings
        for (let j = values.length; j < headers.length; j++) {
            rowData[headers[j]] = '';
        }
        
        data[testName] = rowData;
        console.log(`Parsed data for test: ${testName}`, rowData);
    }
    
    return { headers, data };
}

/**
 * Helper function to parse a CSV line, handling quoted values and commas within quotes
 * @param {string} line - The CSV line to parse
 * @returns {Array} - Array of values
 */
function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
        const char = line[i];
        
        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current);
            current = '';
        } else {
            current += char;
        }
    }
    
    // Add the last value
    result.push(current);
    
    return result;
}
