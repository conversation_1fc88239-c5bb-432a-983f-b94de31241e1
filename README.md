# Google Sheet to JSON Updater

This tool updates the `TruScan_EXECUTIVE HEALTH PACKAGE.json` file with data from a Google Sheet.

## Prerequisites

1. Python 3.6 or higher
2. Required Python packages:
   - pandas
   - google-api-python-client
   - oauth2client
   - gspread

## Setup Instructions

### 1. Install Required Packages

```bash
pip install pandas google-api-python-client oauth2client gspread
```

### 2. Set Up Google Sheets API Credentials

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google Sheets API:
   - In the sidebar, click on "APIs & Services" > "Library"
   - Search for "Google Sheets API" and enable it
4. Create credentials:
   - In the sidebar, click on "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "Service Account"
   - Fill in the service account details and click "Create"
   - Skip the optional steps and click "Done"
5. Create and download the key:
   - In the service accounts list, find the one you just created
   - Click on the three dots menu > "Manage keys"
   - Click "Add Key" > "Create new key"
   - Choose <PERSON> format and click "Create"
   - The key file will be downloaded to your computer
6. Rename the downloaded file to `credentials.json` and place it in the same directory as the script

### 3. Share the Google Sheet

1. Open your Google Sheet
2. Click the "Share" button in the top right
3. Add the email address from the service account (found in the credentials.json file, look for "client_email")
4. Give it at least "Viewer" access
5. Click "Done"

## Usage

Run the script:

```bash
python update_json_from_sheet.py
```

The script will:
1. Connect to the Google Sheet
2. Extract the data
3. Update the JSON file with the corresponding values

## Troubleshooting

- If you get authentication errors, make sure:
  - The credentials.json file is in the same directory as the script
  - The Google Sheet has been shared with the service account email
  - The Google Sheets API is enabled for your project
- If data isn't being updated correctly, check:
  - The column names in the Google Sheet match what the script expects
  - The test names in the Google Sheet match those in the JSON file
