#!/usr/bin/env python3
import json
import os
import sys
import pandas as pd
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build

def get_sheet_data(sheet_id, sheet_range):
    """
    Get data from Google Sheet using the Sheets API.
    Requires a credentials.json file with appropriate permissions.
    """
    try:
        # Check if credentials file exists
        if not os.path.exists('credentials.json'):
            print("Error: credentials.json file not found.")
            print("Please create a service account and download the credentials file.")
            print("See: https://developers.google.com/sheets/api/quickstart/python")
            sys.exit(1)
        
        # Set up credentials
        SCOPES = ['https://www.googleapis.com/auth/spreadsheets.readonly']
        creds = Credentials.from_service_account_file('credentials.json', scopes=SCOPES)
        
        # Build the service
        service = build('sheets', 'v4', credentials=creds)
        
        # Call the Sheets API
        sheet = service.spreadsheets()
        result = sheet.values().get(spreadsheetId=sheet_id, range=sheet_range).execute()
        values = result.get('values', [])
        
        if not values:
            print('No data found in the sheet.')
            return None
        
        # Convert to DataFrame
        df = pd.DataFrame(values[1:], columns=values[0])
        return df
    
    except Exception as e:
        print(f"Error accessing Google Sheet: {e}")
        return None

def update_json_with_sheet_data(json_file, sheet_data):
    """
    Update the JSON file with data from the Google Sheet.
    """
    try:
        # Load the JSON file
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        # Create a dictionary to map test names to their data from the sheet
        test_data_map = {}
        for _, row in sheet_data.iterrows():
            test_name = row.get('Test Name', '').strip()
            if test_name:
                test_data_map[test_name] = {
                    'about': row.get('About Test', ''),
                    'cause': row.get('Cause of abnormal result', ''),
                    'impact': row.get('Impact', ''),
                    'improve': row.get('How to improve', '')
                }
        
        # Update the JSON data
        updated_count = 0
        for item in data.get('summary_items', []):
            test_name = item.get('testName', '')
            if test_name in test_data_map:
                sheet_info = test_data_map[test_name]
                
                # Update the suggestions
                for suggestion in item.get('suggestions', []):
                    suggestion_id = suggestion.get('id', '')
                    
                    if suggestion_id == 'about-test':
                        suggestion['content'] = sheet_info['about']
                        updated_count += 1
                    elif suggestion_id == 'cause-of-abnormal-result':
                        suggestion['content'] = sheet_info['cause']
                        updated_count += 1
                    elif suggestion_id == 'impact':
                        suggestion['content'] = sheet_info['impact']
                        updated_count += 1
                    elif suggestion_id == 'how-to-improve':
                        suggestion['content'] = sheet_info['improve']
                        updated_count += 1
        
        # Save the updated JSON
        with open(json_file, 'w') as f:
            json.dump(data, f, indent=2)
        
        print(f"Updated {updated_count} fields in the JSON file.")
        return True
    
    except Exception as e:
        print(f"Error updating JSON: {e}")
        return False

def main():
    # Google Sheet ID from the URL
    sheet_id = '1rsODoa2JMNIH4Rxd6jhGW0kWLsU3aN3i_9Q8orQgWBE'
    sheet_range = 'Sheet1!A:E'  # Adjust this range as needed
    json_file = 'TruScan_EXECUTIVE HEALTH PACKAGE.json'
    
    print(f"Fetching data from Google Sheet: {sheet_id}")
    sheet_data = get_sheet_data(sheet_id, sheet_range)
    
    if sheet_data is not None:
        print(f"Found {len(sheet_data)} rows of data in the sheet.")
        success = update_json_with_sheet_data(json_file, sheet_data)
        if success:
            print("Successfully updated the JSON file.")
        else:
            print("Failed to update the JSON file.")
    else:
        print("Failed to fetch data from the Google Sheet.")

if __name__ == "__main__":
    main()
