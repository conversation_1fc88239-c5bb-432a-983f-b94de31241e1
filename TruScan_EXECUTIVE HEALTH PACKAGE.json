{"summary_items": [{"index": 2, "test_id": 3981721, "suggestions": [{"id": "about-test", "title": "About Test", "content": "Measures the amount of vitamin B12 in the blood, which is essential for nerve function and red blood cell production. It is used to diagnose vitamin B12 deficiency and anemia."}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "Abnormal vitamin B12 levels might be caused by dietary deficiencies, malabsorption disorders, or certain medications. Low levels can lead to anemia, neurological symptoms, and fatigue, affecting overall health."}, {"id": "impact", "title": "Impact", "content": "Low vitamin B12 levels can lead to neurological issues, anemia, and fatigue. Supplementation or dietary adjustments may be required to prevent long-term complications, such as cognitive decline."}, {"id": "how-to-improve", "title": "How to improve?", "content": "To maintain healthy B12 levels, include B12-rich foods like meat, fish, dairy, and eggs. Regular blood tests, especially for vegetarians or older adults, help monitor B12 status and ensure optimal absorption for overall health."}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981721, "testName": "VIT B12", "report_name": "VITAMIN B12", "dictionary_id": 1035, "report_format_pk": 22477745}, {"index": 5, "test_id": 3981721, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981721, "testName": "Comments:", "report_name": "VITAMIN B12", "dictionary_id": null, "report_format_pk": 22477746}, {"index": 3, "test_id": 3981721, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981721, "testName": "Interpretation:", "report_name": "VITAMIN B12", "dictionary_id": null, "report_format_pk": 22477747}, {"index": 9, "test_id": 3981932, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981932, "testName": "T3-Total*", "report_name": "THYROID FUNCTION TESTS ( TFT )", "dictionary_id": 1024, "report_format_pk": 65998118}, {"index": 4, "test_id": 3981932, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981932, "testName": "TOTAL T4", "report_name": "THYROID FUNCTION TESTS ( TFT )", "dictionary_id": null, "report_format_pk": 65998119}, {"index": 10, "test_id": 3981932, "suggestions": [{"id": "about-test", "title": "About Test", "content": "Measures the level of thyroid-stimulating hormone (TSH) in the blood. It helps diagnose thyroid disorders like hypothyroidism, hyperthyroidism, or pituitary dysfunction."}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "Abnormal results can be caused by hypothyroidism (high TSH levels), suggesting an underactive thyroid, or hyperthyroidism (low TSH levels), indicating an overactive thyroid, impacting metabolism and energy levels."}, {"id": "impact", "title": "Impact", "content": "High or low TSH levels indicate thyroid issues such as hypo or hyperthyroidism, requiring intervention to manage thyroid hormone levels and overall health."}, {"id": "how-to-improve", "title": "How to improve?", "content": "If TSH levels are abnormal, consult a healthcare provider for further assessment and management. Support thyroid function with a balanced diet, regular physical activity, and effective stress management techniques."}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981932, "testName": "TSH", "report_name": "THYROID FUNCTION TESTS ( TFT )", "dictionary_id": null, "report_format_pk": 65998120}, {"index": 7, "test_id": 3981932, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981932, "testName": "Comments:", "report_name": "THYROID FUNCTION TESTS ( TFT )", "dictionary_id": null, "report_format_pk": 65998121}, {"index": 6, "test_id": 3981932, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981932, "testName": "Interpretation", "report_name": "THYROID FUNCTION TESTS ( TFT )", "dictionary_id": 302, "report_format_pk": 65998122}, {"index": 2, "test_id": 3981937, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981937, "testName": "PSA TOTAL", "report_name": "PSA TOTAL", "dictionary_id": 336, "report_format_pk": 71932505}, {"index": 3, "test_id": 3981937, "suggestions": [], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981937, "testName": "&nbsp", "report_name": "PSA TOTAL", "dictionary_id": null, "report_format_pk": 71932506}, {"index": 4, "test_id": 3981937, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981937, "testName": "Note", "report_name": "PSA TOTAL", "dictionary_id": null, "report_format_pk": 71932507}, {"index": 6, "test_id": 3981942, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981942, "testName": "vita", "report_name": "VITAMIN D 3", "dictionary_id": 1021, "report_format_pk": 52970028}, {"index": 2, "test_id": 3981942, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981942, "testName": "VIT D", "report_name": "VITAMIN D 3", "dictionary_id": 414, "report_format_pk": 55592480}, {"index": 5, "test_id": 3981942, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981942, "testName": "Comments:", "report_name": "VITAMIN D 3", "dictionary_id": null, "report_format_pk": 55592481}, {"index": 3, "test_id": 3981942, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981942, "testName": "Interpretation:", "report_name": "VITAMIN D 3", "dictionary_id": null, "report_format_pk": 55592482}, {"index": 7, "test_id": 3981960, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981960, "testName": "-", "report_name": "Glucose -Fasting", "dictionary_id": 1021, "report_format_pk": 20557820}, {"index": 6, "test_id": 3981960, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981960, "testName": "-", "report_name": "Glucose -Fasting", "dictionary_id": 1021, "report_format_pk": 20557821}, {"index": 8, "test_id": 3981960, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981960, "testName": "Fasting Urine <PERSON>", "report_name": "Glucose -Fasting", "dictionary_id": null, "report_format_pk": 20557822}, {"index": 9, "test_id": 3981960, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981960, "testName": "Fasting Urine <PERSON>", "report_name": "Glucose -Fasting", "dictionary_id": null, "report_format_pk": 20557831}, {"index": 10, "test_id": 3981960, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981960, "testName": "Fasting Urine <PERSON>", "report_name": "Glucose -Fasting", "dictionary_id": 959, "report_format_pk": 20557848}, {"index": 12, "test_id": 3981960, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981960, "testName": "Blood Glucose Fasting*", "report_name": "Glucose -Fasting", "dictionary_id": 959, "report_format_pk": 20557849}, {"index": 2, "test_id": 3981960, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981960, "testName": "Glucose Fasting*", "report_name": "Glucose -Fasting", "dictionary_id": 959, "report_format_pk": 76788791}, {"index": 11, "test_id": 3981960, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981960, "testName": "Urine Glucose Fasting*", "report_name": "Glucose -Fasting", "dictionary_id": 959, "report_format_pk": 76788792}, {"index": 3, "test_id": 3981960, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981960, "testName": "Note", "report_name": "Glucose -Fasting", "dictionary_id": null, "report_format_pk": 76788793}, {"index": 5, "test_id": 3981960, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981960, "testName": "-", "report_name": "Glucose -Fasting", "dictionary_id": 1021, "report_format_pk": 76788794}, {"index": 3, "test_id": 3981962, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981962, "testName": "Note", "report_name": "Glucose - Post Prandial(PP)", "dictionary_id": null, "report_format_pk": 20557842}, {"index": 4, "test_id": 3981962, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981962, "testName": "Post Prandial Urine Glucose", "report_name": "Glucose - Post Prandial(PP)", "dictionary_id": 960, "report_format_pk": 20557843}, {"index": 5, "test_id": 3981962, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981962, "testName": "Urine Post Prandial  Glucose", "report_name": "Glucose - Post Prandial(PP)", "dictionary_id": 960, "report_format_pk": 20630946}, {"index": 2, "test_id": 3981962, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981962, "testName": "Blood Glucose-Post Prandial*", "report_name": "Glucose - Post Prandial(PP)", "dictionary_id": 960, "report_format_pk": 76788800}, {"index": 6, "test_id": 3981962, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981962, "testName": "<PERSON><PERSON>-Post Prandial*", "report_name": "Glucose - Post Prandial(PP)", "dictionary_id": 960, "report_format_pk": 76788801}, {"index": 2, "test_id": 3981964, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981964, "testName": "Cholesterol-Total", "report_name": "Lipid Profile", "dictionary_id": 955, "report_format_pk": 68813318}, {"index": 3, "test_id": 3981964, "suggestions": [{"id": "about-test", "title": "About Test", "content": "Triglycerides are a type of fat found in the blood and are used to assess heart disease risk and metabolic health. High levels are associated with cardiovascular issues."}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "Elevated triglyceride levels can be caused by high-fat, sugary diets, obesity, and poorly controlled diabetes."}, {"id": "impact", "title": "Impact", "content": "Abnormal results can increase the risk of heart disease, stroke, and pancreatitis. High triglyceride levels may signal metabolic syndrome, while low levels are uncommon and may point to malnutrition or metabolic issues."}, {"id": "how-to-improve", "title": "How to improve?", "content": "High CRP levels indicate inflammation. Investigate the source of inflammation, such as infections or autoimmune conditions, and manage chronic conditions with medication to reduce CRP and inflammation."}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981964, "testName": "Triglycerides", "report_name": "Lipid Profile", "dictionary_id": 154, "report_format_pk": 68813319}, {"index": 4, "test_id": 3981964, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981964, "testName": "Cholesterol-HDL Direct", "report_name": "Lipid Profile", "dictionary_id": 953, "report_format_pk": 68813320}, {"index": 5, "test_id": 3981964, "suggestions": [{"id": "about-test", "title": "About Test", "content": "LDL (Low-Density Lipoprotein) cholesterol is the \"bad\" cholesterol that can build up in arteries, increasing heart disease and stroke risk. High LDL levels cause plaque formation, narrowing blood vessels. A healthy diet and exercise help lower LDL and improve heart health."}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "Abnormal results can be caused by high LDL cholesterol, which is a major risk factor for cardiovascular diseases, including coronary artery disease and stroke. Elevated LDL levels contribute to plaque buildup in arteries."}, {"id": "impact", "title": "Impact", "content": "High LDL cholesterol levels can increase the risk of heart disease and stroke, requiring interventions to lower levels and manage overall cardiovascular health."}, {"id": "how-to-improve", "title": "How to improve?", "content": "Limit saturated fats, include heart-healthy fats, and exercise regularly to lower LDL cholesterol. Medications like statins may be recommended by a healthcare provider based on routine cholesterol screenings."}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981964, "testName": "LDL Cholesterol", "report_name": "Lipid Profile", "dictionary_id": 952, "report_format_pk": 68813321}, {"index": 11, "test_id": 3981964, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981964, "testName": "Non - HDL Cholesterol, Serum", "report_name": "Lipid Profile", "dictionary_id": 1495, "report_format_pk": 68813322}, {"index": 6, "test_id": 3981964, "suggestions": [{"id": "about-test", "title": "About Test", "content": "Measures the level of very-low-density lipoprotein (VLDL) cholesterol, which is involved in fat transport in the blood. High VLDL levels can increase the risk of cardiovascular disease."}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "Abnormal results can be caused by high VLDL cholesterol, which increases the risk of atherosclerosis, heart disease, and stroke due to fat buildup in arteries. Elevated VLDL levels often correlate with metabolic syndrome."}, {"id": "impact", "title": "Impact", "content": "Elevated VLDL cholesterol levels are associated with an increased risk of plaque buildup in arteries, contributing to the development of atherosclerosis and cardiovascular diseases over time."}, {"id": "how-to-improve", "title": "How to improve?", "content": "Lower VLDL cholesterol by reducing refined carbohydrates and sugary foods. Regular physical activity and a healthy weight help maintain balanced lipid profiles."}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981964, "testName": "VLDL Cholesterol", "report_name": "Lipid Profile", "dictionary_id": 956, "report_format_pk": 68813323}, {"index": 7, "test_id": 3981964, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981964, "testName": "CHOL/HDL RATIO", "report_name": "Lipid Profile", "dictionary_id": null, "report_format_pk": 68813324}, {"index": 19, "test_id": 3981964, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981964, "testName": "LDL/HDL RATIO", "report_name": "Lipid Profile", "dictionary_id": 499, "report_format_pk": 68813325}, {"index": 20, "test_id": 3981964, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981964, "testName": "HDL/LDL RATIO", "report_name": "Lipid Profile", "dictionary_id": 499, "report_format_pk": 68813326}, {"index": 9, "test_id": 3981964, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3981964, "testName": "<b>Note:</b> 8-10 hours fasting sample is required.", "report_name": "Lipid Profile", "dictionary_id": 1021, "report_format_pk": 68813327}, {"index": 6, "test_id": 4005393, "suggestions": [], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005393, "testName": "&nbsp", "report_name": "ECHOCARDIOGRAPHY", "dictionary_id": null, "report_format_pk": 53062875}, {"index": 5, "test_id": 4005393, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005393, "testName": "Impression:", "report_name": "ECHOCARDIOGRAPHY", "dictionary_id": null, "report_format_pk": 53062876}, {"index": 3, "test_id": 4005393, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005393, "testName": "TEST PERFORMED BY ", "report_name": "ECHOCARDIOGRAPHY", "dictionary_id": null, "report_format_pk": 53062878}, {"index": 1, "test_id": 4005393, "suggestions": [], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005393, "testName": "&nbsp", "report_name": "ECHOCARDIOGRAPHY", "dictionary_id": null, "report_format_pk": 53062880}, {"index": 8, "test_id": 4005393, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005393, "testName": "ECHOCARDIOGRAPHY", "report_name": "ECHOCARDIOGRAPHY", "dictionary_id": 1021, "report_format_pk": 53062881}, {"index": 4, "test_id": 4005393, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005393, "testName": "DR VITHAL BHAGI ", "report_name": "ECHOCARDIOGRAPHY", "dictionary_id": null, "report_format_pk": 53062882}, {"index": 1, "test_id": 4005393, "suggestions": [], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005393, "testName": "&nbsp", "report_name": "ECHOCARDIOGRAPHY", "dictionary_id": null, "report_format_pk": 53062883}, {"index": 7, "test_id": 4005393, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005393, "testName": "impression", "report_name": "ECHOCARDIOGRAPHY", "dictionary_id": null, "report_format_pk": 53062884}, {"index": 9, "test_id": 4005393, "suggestions": [], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005393, "testName": "&nbsp", "report_name": "ECHOCARDIOGRAPHY", "dictionary_id": null, "report_format_pk": 75272809}, {"index": 10, "test_id": 4005393, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005393, "testName": "INTERPRETATION", "report_name": "ECHOCARDIOGRAPHY", "dictionary_id": null, "report_format_pk": 75272810}, {"index": 7, "test_id": 4005399, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005399, "testName": "-", "report_name": "ULTRASOUND ABDOMEN-PELVIS", "dictionary_id": 1021, "report_format_pk": 53062998}, {"index": 1, "test_id": 4005399, "suggestions": [], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005399, "testName": "&nbsp", "report_name": "ULTRASOUND ABDOMEN-PELVIS", "dictionary_id": null, "report_format_pk": 53062999}, {"index": 2, "test_id": 4005399, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005399, "testName": "Impression:", "report_name": "ULTRASOUND ABDOMEN-PELVIS", "dictionary_id": null, "report_format_pk": 53063000}, {"index": 11, "test_id": 4005399, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005399, "testName": "ULTRASOUND AB<PERSON>OMEN AND PELVIS", "report_name": "ULTRASOUND ABDOMEN-PELVIS", "dictionary_id": 1021, "report_format_pk": 53063001}, {"index": 5, "test_id": 4005399, "suggestions": [], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005399, "testName": "&nbsp", "report_name": "ULTRASOUND ABDOMEN-PELVIS", "dictionary_id": null, "report_format_pk": 53063002}, {"index": 6, "test_id": 4005399, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005399, "testName": "ULTRASOUND ABDOMEN-PELVIS", "report_name": "ULTRASOUND ABDOMEN-PELVIS", "dictionary_id": 1021, "report_format_pk": 53063003}, {"index": 13, "test_id": 4005399, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005399, "testName": "ULTRASOUND ABDOMEN-PELVIS", "report_name": "ULTRASOUND ABDOMEN-PELVIS", "dictionary_id": 1021, "report_format_pk": 53063005}, {"index": 3, "test_id": 4005399, "suggestions": [], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005399, "testName": "&nbsp", "report_name": "ULTRASOUND ABDOMEN-PELVIS", "dictionary_id": null, "report_format_pk": 53063006}, {"index": 4, "test_id": 4005399, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005399, "testName": "IMPRESSION: ", "report_name": "ULTRASOUND ABDOMEN-PELVIS", "dictionary_id": null, "report_format_pk": 53063007}, {"index": 9, "test_id": 4005399, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005399, "testName": "Impression:", "report_name": "ULTRASOUND ABDOMEN-PELVIS", "dictionary_id": null, "report_format_pk": 53063008}, {"index": 12, "test_id": 4005399, "suggestions": [], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005399, "testName": "&nbsp", "report_name": "ULTRASOUND ABDOMEN-PELVIS", "dictionary_id": null, "report_format_pk": 53183164}, {"index": 8, "test_id": 4005399, "suggestions": [], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005399, "testName": "&nbsp", "report_name": "ULTRASOUND ABDOMEN-PELVIS", "dictionary_id": null, "report_format_pk": 53183165}, {"index": 14, "test_id": 4005399, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005399, "testName": "ULTRASOUND ABDOMEN-PELVIS", "report_name": "ULTRASOUND ABDOMEN-PELVIS", "dictionary_id": 1021, "report_format_pk": 67536803}, {"index": 15, "test_id": 4005399, "suggestions": [], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005399, "testName": "&nbsp", "report_name": "ULTRASOUND ABDOMEN-PELVIS", "dictionary_id": null, "report_format_pk": 67536804}, {"index": 10, "test_id": 4005399, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4005399, "testName": "Impression:", "report_name": "ULTRASOUND ABDOMEN-PELVIS", "dictionary_id": null, "report_format_pk": 67536805}, {"index": 1, "test_id": 4015240, "suggestions": [], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4015240, "testName": "&nbsp", "report_name": "X-RAY CHEST AP/PA", "dictionary_id": null, "report_format_pk": 52865075}, {"index": 2, "test_id": 4015240, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4015240, "testName": "Impression:", "report_name": "X-RAY CHEST AP/PA", "dictionary_id": null, "report_format_pk": 52865076}, {"index": 6, "test_id": 4015240, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4015240, "testName": "X – RAY  CHEST AP/PA  VIEW", "report_name": "X-RAY CHEST AP/PA", "dictionary_id": 1021, "report_format_pk": 52896799}, {"index": 7, "test_id": 4015240, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4015240, "testName": "X – RAY  CHEST AP/PA  VIEW", "report_name": "X-RAY CHEST AP/PA", "dictionary_id": 1021, "report_format_pk": 52896800}, {"index": 3, "test_id": 4015240, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4015240, "testName": "X – RAY  CHEST AP/PA  VIEW", "report_name": "X-RAY CHEST AP/PA", "dictionary_id": null, "report_format_pk": 52896801}, {"index": 4, "test_id": 4015240, "suggestions": [], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4015240, "testName": "&nbsp", "report_name": "X-RAY CHEST AP/PA", "dictionary_id": null, "report_format_pk": 52903967}, {"index": 5, "test_id": 4015240, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4015240, "testName": "Impression:", "report_name": "X-RAY CHEST AP/PA", "dictionary_id": null, "report_format_pk": 52903968}, {"index": 8, "test_id": 4015240, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4015240, "testName": "X-RAY CHEST ", "report_name": "X-RAY CHEST AP/PA", "dictionary_id": 1021, "report_format_pk": 52906087}, {"index": 10, "test_id": 4015240, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4015240, "testName": "Impression:", "report_name": "X-RAY CHEST AP/PA", "dictionary_id": null, "report_format_pk": 52921694}, {"index": 9, "test_id": 4015240, "suggestions": [], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4015240, "testName": "&nbsp", "report_name": "X-RAY CHEST AP/PA", "dictionary_id": null, "report_format_pk": 52922476}, {"index": 12, "test_id": 4015240, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4015240, "testName": "Impression:", "report_name": "X-RAY CHEST AP/PA", "dictionary_id": null, "report_format_pk": 52922477}, {"index": 11, "test_id": 4015240, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4015240, "testName": "X-RAY CHEST AP/PA", "report_name": "X-RAY CHEST AP/PA", "dictionary_id": 1021, "report_format_pk": 70423395}, {"index": 12, "test_id": 4015240, "suggestions": [], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4015240, "testName": "&nbsp", "report_name": "X-RAY CHEST AP/PA", "dictionary_id": null, "report_format_pk": 70423396}, {"index": 13, "test_id": 4015240, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4015240, "testName": "Impression:", "report_name": "X-RAY CHEST AP/PA", "dictionary_id": null, "report_format_pk": 70423397}, {"index": 6, "test_id": 4024782, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4024782, "testName": "TEST PERFORMED BY", "report_name": "ECG ", "dictionary_id": null, "report_format_pk": 52863861}, {"index": 7, "test_id": 4024782, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4024782, "testName": "IMPRESSION", "report_name": "ECG ", "dictionary_id": null, "report_format_pk": 52863862}, {"index": 4, "test_id": 4024782, "suggestions": [], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4024782, "testName": "&nbsp", "report_name": "ECG ", "dictionary_id": null, "report_format_pk": 52905277}, {"index": 7, "test_id": 4024782, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4024782, "testName": "Impression:", "report_name": "ECG ", "dictionary_id": null, "report_format_pk": 52978941}, {"index": 6, "test_id": 4024782, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4024782, "testName": "Electrocardiogram", "report_name": "ECG ", "dictionary_id": 1021, "report_format_pk": 53023074}, {"index": 5, "test_id": 4024782, "suggestions": [], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4024782, "testName": "&nbsp", "report_name": "ECG ", "dictionary_id": null, "report_format_pk": 53023075}, {"index": 8, "test_id": 4024782, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4024782, "testName": "Impression:", "report_name": "ECG ", "dictionary_id": null, "report_format_pk": 53023076}, {"index": 14, "test_id": 4030134, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030134, "testName": "GGT-Gamma Glutamyl Transpeptidae", "report_name": "LIVER FUNCTION TEST ( LFT )", "dictionary_id": 294, "report_format_pk": 20342507}, {"index": 26, "test_id": 4030134, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030134, "testName": "GGT-Gamma Glutamyl Transpeptidase", "report_name": "LIVER FUNCTION TEST ( LFT )", "dictionary_id": null, "report_format_pk": 20453635}, {"index": 27, "test_id": 4030134, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030134, "testName": "Note:", "report_name": "LIVER FUNCTION TEST ( LFT )", "dictionary_id": null, "report_format_pk": 20453636}, {"index": 28, "test_id": 4030134, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030134, "testName": "GGT-Gamma Glutamyl Transpeptidase", "report_name": "LIVER FUNCTION TEST ( LFT )", "dictionary_id": 505, "report_format_pk": 20453654}, {"index": 23, "test_id": 4030134, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030134, "testName": "SGOT/SGPT", "report_name": "LIVER FUNCTION TEST ( LFT )", "dictionary_id": null, "report_format_pk": 20587858}, {"index": 24, "test_id": 4030134, "suggestions": [{"id": "about-test", "title": "About Test", "content": "A test to measure the total amount of bilirubin in the blood. High levels may indicate liver problems, bile duct obstruction, or red blood cell destruction, resulting in jaundice."}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "High total bilirubin levels are caused by liver disease, bile duct obstruction, or hemolysis, leading to jaundice as bilirubin accumulates in the blood."}, {"id": "impact", "title": "Impact", "content": "High total bilirubin levels can cause liver disease or bile duct obstruction, leading to jaundice, dark urine, and abdominal pain."}, {"id": "how-to-improve", "title": "How to improve?", "content": "Treat liver conditions like hepatitis or cirrhosis with appropriate medications, avoid alcohol, and follow a healthy diet to support liver function."}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030134, "testName": "Bilirubin - Total", "report_name": "LIVER FUNCTION TEST ( LFT )", "dictionary_id": 426, "report_format_pk": 75574445}, {"index": 10, "test_id": 4030134, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030134, "testName": "Bilirubin - Direct", "report_name": "LIVER FUNCTION TEST ( LFT )", "dictionary_id": 472, "report_format_pk": 75574446}, {"index": 11, "test_id": 4030134, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030134, "testName": "Bilirubin - Indirect", "report_name": "LIVER FUNCTION TEST ( LFT )", "dictionary_id": 473, "report_format_pk": 75574447}, {"index": 12, "test_id": 4030134, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030134, "testName": "SGOT (AST)", "report_name": "LIVER FUNCTION TEST ( LFT )", "dictionary_id": 982, "report_format_pk": 75574448}, {"index": 13, "test_id": 4030134, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030134, "testName": "SGPT (ALT)", "report_name": "LIVER FUNCTION TEST ( LFT )", "dictionary_id": 976, "report_format_pk": 75574449}, {"index": 25, "test_id": 4030134, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030134, "testName": "Alkaline Phosphatase-ALPI", "report_name": "LIVER FUNCTION TEST ( LFT )", "dictionary_id": 409, "report_format_pk": 75574450}, {"index": 3, "test_id": 4030134, "suggestions": [{"id": "about-test", "title": "About Test", "content": "Measures total protein levels in the blood to assess overall protein status."}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "Abnormal results can cause kidney damage or affect the immune system. High levels may indicate kidney disease, while low levels can lead to symptoms like fatigue, swelling, weakened immunity, and poor wound healing."}, {"id": "impact", "title": "Impact", "content": "High levels may indicate infection, inflammation, or multiple myeloma. Low levels may suggest liver or kidney disorders, malnutrition, or bleeding."}, {"id": "how-to-improve", "title": "How to improve?", "content": "Improving diet with protein-rich foods like eggs, dairy, and legumes can help improve levels."}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030134, "testName": "Total Protein", "report_name": "LIVER FUNCTION TEST ( LFT )", "dictionary_id": 662, "report_format_pk": 75574451}, {"index": 4, "test_id": 4030134, "suggestions": [{"id": "about-test", "title": "About Test", "content": "A protein produced by the liver that helps maintain fluid balance and transport substances in the blood. Low levels can indicate liver disease, kidney disease, or malnutrition."}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "Abnormal albumin levels can be caused by liver disease, kidney disorders, dehydration, malnutrition, inflammation, or conditions that affect the body's ability to produce or retain albumin, such as infections or chronic illnesses."}, {"id": "impact", "title": "Impact", "content": "Low albumin levels can cause liver or kidney disease, malnutrition, or inflammation, leading to symptoms like swelling, fatigue, and slow wound healing."}, {"id": "how-to-improve", "title": "How to improve?", "content": "Improve nutritional intake, address liver or kidney disease, and manage conditions like dehydration or malnutrition that affect albumin levels."}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030134, "testName": "Albumin", "report_name": "LIVER FUNCTION TEST ( LFT )", "dictionary_id": 240, "report_format_pk": 75574452}, {"index": 5, "test_id": 4030134, "suggestions": [{"id": "about-test", "title": "About Test", "content": "A blood test measuring globulins, proteins made by the liver and immune system, to assess liver, kidney function, and immune health."}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "Abnormal results can cause malnutrition, poor immunity, and liver/kidney dysfunction. High levels may indicate infection, inflammation, or cancer, while low levels may suggest liver disease or immune system issues."}, {"id": "impact", "title": "Impact", "content": "Low globulin levels may indicate liver disease, kidney disease, or malnutrition. High levels may be linked to infections, inflammatory diseases, or cancers."}, {"id": "how-to-improve", "title": "How to improve?", "content": "Dietary changes include a balanced diet with adequate protein and anti-inflammatory foods like fatty fish, nuts, and leafy greens. Lifestyle modifications involve maintaining a healthy weight, exercising, avoiding excessive alcohol, and managing chronic infections. Medical interventions focus on treating underlying conditions and managing liver or kidney health."}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030134, "testName": "Globulin", "report_name": "LIVER FUNCTION TEST ( LFT )", "dictionary_id": 506, "report_format_pk": 75574453}, {"index": 6, "test_id": 4030134, "suggestions": [{"id": "about-test", "title": "About Test", "content": "The A/G ratio is a lab test result that compares the amount of albumin to the amount of globulin in your blood. It's calculated by dividing the albumin level by the globulin level, giving a numerical value. This value is used by healthcare providers as part of a broader health assessment.\n"}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "Abnormal results can be due to an imbalance between albumin and globulin proteins in the blood. This can stem from liver or kidney disease, inflammation, infection, or certain immune disorders affecting protein production or loss. Medications or nutritional deficiencies might also contribute.\n"}, {"id": "impact", "title": "Impact", "content": "High results can cause the body's fluid balance to shift, potentially leading to swelling or liver problems. Low results can cause a weakened immune system, making the body more susceptible to infections or indicating kidney disease.\n"}, {"id": "how-to-improve", "title": "How to improve?", "content": "To improve an abnormal albumin/globulin (A/G) ratio, focus on supporting liver health through a balanced diet, avoiding excessive alcohol, and managing underlying conditions. Maintaining hydration and consulting with a healthcare provider about potential medication adjustments can also help normalize the ratio over time.\n"}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030134, "testName": "A/G Ratio", "report_name": "LIVER FUNCTION TEST ( LFT )", "dictionary_id": 505, "report_format_pk": 75574454}, {"index": 29, "test_id": 4030134, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030134, "testName": "GGT-Gamma Glutamyl Transpeptidase", "report_name": "LIVER FUNCTION TEST ( LFT )", "dictionary_id": 505, "report_format_pk": 75574455}, {"index": 22, "test_id": 4030134, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030134, "testName": "Note:", "report_name": "LIVER FUNCTION TEST ( LFT )", "dictionary_id": null, "report_format_pk": 75574456}, {"index": 10, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Total Leucocytes (WBC) Count*", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 784, "report_format_pk": 20450522}, {"index": 46, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Absolute Basophils Count*", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 1124, "report_format_pk": 20453686}, {"index": 68, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Differential count :", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 122, "report_format_pk": 71559409}, {"index": 3, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Hemoglobin (Hb)", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 302, "report_format_pk": 76862910}, {"index": 2, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Erythrocyte (RBC) Count", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 339, "report_format_pk": 76862911}, {"index": 4, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Packed Cell Volume (PCV)", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 301, "report_format_pk": 76862912}, {"index": 5, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Mean Cell Volume (MCV)", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 416, "report_format_pk": 76862913}, {"index": 6, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Mean Cell Haemoglobin (MCH)", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 417, "report_format_pk": 76862914}, {"index": 7, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Mean Corpuscular Hb Concn. (MCHC)", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 418, "report_format_pk": 76862915}, {"index": 8, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": "RDW measures the variation in the size of red blood cells. High RDW may indicate anemia, vitamin deficiencies, or other blood disorders."}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "Caused by anemia, vitamin or iron deficiencies, or bone marrow disorders, leading to variation in the size of red blood cells."}, {"id": "impact", "title": "Impact", "content": "Elevated RDW can cause anemia, leading to fatigue, weakness, and shortness of breath."}, {"id": "how-to-improve", "title": "How to improve?", "content": "Improve RDW by increasing iron-rich foods like spinach, red meat, and beans in your diet. Adding folate and vitamin B12-rich foods may also help, as deficiencies in these nutrients can affect red blood cell production."}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Red Cell Distribution Width (RDW)", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 419, "report_format_pk": 76862916}, {"index": 66, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Total Leucocytes (WBC) Count", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 784, "report_format_pk": 76862917}, {"index": 11, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Neutrophils", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 420, "report_format_pk": 76862918}, {"index": 12, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Lymphocytes", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 421, "report_format_pk": 76862919}, {"index": 13, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Monocytes", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 424, "report_format_pk": 76862920}, {"index": 14, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Eosinophils", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 423, "report_format_pk": 76862921}, {"index": 15, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Basophils", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 422, "report_format_pk": 76862922}, {"index": 42, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Absolute Neutrophil Count", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 1121, "report_format_pk": 76862923}, {"index": 43, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": "Measures the number of lymphocytes (a type of white blood cell) in the blood. This count helps assess immune function and can indicate infections or immune system disorders."}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "Abnormal lymphocyte counts might be caused by viral infections, stress, or autoimmune disorders. High lymphocyte levels may indicate autoimmune diseases or certain cancers, while low levels can compromise immune defense."}, {"id": "impact", "title": "Impact", "content": "Low lymphocyte counts may signal immune deficiencies, while high levels can indicate viral infections or leukemia. Close monitoring and further tests are necessary to pinpoint the underlying cause."}, {"id": "how-to-improve", "title": "How to improve?", "content": "Abnormal lymphocyte counts should prompt further assessment by a healthcare provider. Maintaining a healthy immune system through regular exercise and a nutritious diet can support normal lymphocyte levels and overall immunity."}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Absolute Lymphocyte Count", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 1122, "report_format_pk": 76862924}, {"index": 44, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": "Measures the number of monocytes (another type of white blood cell) in blood. This test is used to evaluate immune function, particularly in response to infections or inflammation."}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "Abnormal results are caused by changes in monocyte count from chronic infections, autoimmune diseases, blood disorders, or inflammatory conditions."}, {"id": "impact", "title": "Impact", "content": "High monocyte counts may indicate chronic inflammatory conditions, infections, or certain cancers, requiring further investigation to determine the exact cause and treatment."}, {"id": "how-to-improve", "title": "How to improve?", "content": "Follow up with a healthcare provider if monocyte counts are abnormal to assess potential causes. A balanced diet and regular physical activity can help support healthy immune function and stabilize monocyte levels."}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Absolute Monocyte Count", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 1123, "report_format_pk": 76862925}, {"index": 45, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Absolute Eosinophil Count", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 431, "report_format_pk": 76862926}, {"index": 9, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": "Measures the number of platelets in blood. Platelets are crucial for blood clotting, and this test helps monitor conditions like bleeding disorders, bone marrow diseases, or infections."}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "Abnormal platelet counts might be caused by thrombocytopenia (low platelet count) or thrombocytosis (high platelet count). Low levels can cause excessive bleeding, while high levels increase the risk of blood clot formation."}, {"id": "impact", "title": "Impact", "content": "Abnormal platelet counts can indicate bleeding disorders, bone marrow issues, or infections. Low platelet count increases bleeding risk, while high counts may lead to clotting issues."}, {"id": "how-to-improve", "title": "How to improve?", "content": "Monitor platelet levels through a balanced diet and lifestyle, avoiding excessive alcohol consumption. In cases of low platelet count, treatment may be necessary for underlying conditions, and medications may be prescribed to stimulate platelet production."}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Platelet Count", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 331, "report_format_pk": 76862927}, {"index": 17, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Mean Platelet Volume (MPV)", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 633, "report_format_pk": 76862928}, {"index": 47, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": "PCT, or procalcitonin, is a protein in the blood. Labs measure its level to see how much is present. A low PCT result is usually normal, while higher results indicate more PCT in the blood. The lab result is reported as a number, like 0.05 ng/mL, showing the concentration of PCT.\n"}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "Abnormal results can be due to various reasons, including infection or inflammation. For PCT, Procalcitonin, elevated levels often indicate a bacterial infection. However, kidney problems or severe trauma can also cause increased PCT, so further testing is needed for confirmation.\n"}, {"id": "impact", "title": "Impact", "content": "High results can cause increased blood clotting, potentially leading to blockages in blood vessels and increasing the risk of thrombosis. Low results can cause impaired blood clotting, potentially leading to excessive bleeding and increasing the risk of hemorrhage.\n"}, {"id": "how-to-improve", "title": "How to improve?", "content": "An abnormal PCT level from a CBC 3 Part test can sometimes be improved by addressing the underlying cause, which is often a bacterial infection. Following your doctor's prescribed antibiotics, staying hydrated, and getting enough rest can help your body fight the infection and potentially lower your PCT level.\n"}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "PCT", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": null, "report_format_pk": 76862929}, {"index": 48, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": "PDW, or platelet distribution width, measures how much the size of your platelets varies. Platelets are tiny cells in your blood that help with clotting. PDW is a value given in percent that indicates if the sizes of your platelets are similar or if there's a wide range of platelet sizes.\n"}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "Abnormal results can be due to variations in platelet size. Platelet Distribution Width (PDW) measures the variability in platelet size, and a high PDW suggests increased platelet production or the presence of larger-than-normal platelets, possibly indicating underlying inflammation, certain diseases, or medications impacting platelet development.\n"}, {"id": "impact", "title": "Impact", "content": "High results can cause the platelets to vary more in size which may indicate active clot formation or increased platelet production in bone marrow. Low results can cause platelets to be uniform in size which may be seen in some cases of inherited platelet disorders or conditions affecting platelet production.\n"}, {"id": "how-to-improve", "title": "How to improve?", "content": "To improve a high PDW (Platelet Distribution Width) in your CBC 3 Part test, focus on overall health. Stay hydrated, eat a balanced diet rich in vitamins and minerals, and manage any underlying inflammatory conditions. Consult your doctor for personalized advice based on your specific situation.\n"}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "PDW", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 1029, "report_format_pk": 76862930}, {"index": 55, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": "Red blood cell count (RBC) measures the number of red blood cells in a specific volume of blood. It's a count of the actual cells, usually expressed as millions per microliter. This value helps determine if you have a typical number of these oxygen-carrying cells circulating in your body.\n"}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "Abnormal results can be due to various reasons. For the RBC parameter in a CBC 3 Part test, a high result might indicate dehydration or a condition causing increased red blood cell production. Conversely, a low result could suggest blood loss, anemia, or bone marrow issues affecting RBC production.\n"}, {"id": "impact", "title": "Impact", "content": "High results can cause the blood to become too thick, potentially leading to clots and hindering oxygen flow to vital organs. Low results can cause the blood to have difficulty carrying enough oxygen, leading to fatigue and shortness of breath, potentially causing anemia.\n"}, {"id": "how-to-improve", "title": "How to improve?", "content": "To improve a low RBC count, focus on increasing iron intake through diet or supplements, ensuring adequate vitamin B12 and folate consumption, and staying hydrated. For a high RBC count, consider drinking more water, reducing smoking or exposure to secondhand smoke, and discussing potential causes with your doctor.\n"}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "RBC", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": null, "report_format_pk": 76862931}, {"index": 69, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Differential count :", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 122, "report_format_pk": 76862932}, {"index": 65, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Platelets", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 122, "report_format_pk": 76862933}, {"index": 18, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": "White blood cell count (WBC) measures the number of white blood cells in a blood sample. This parameter helps assess the body's ability to fight infection. The CBC 3-part differential further categorizes WBCs into three groups, lymphocytes, monocytes, and granulocytes, providing a basic breakdown of the different types of immune cells present.\n"}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "Abnormal results can be due to a variety of reasons. For white blood cells (WBC), a high count may indicate an infection or inflammation. A low count could be caused by certain medications, bone marrow problems, or autoimmune disorders, affecting the body's ability to fight off illness.\n"}, {"id": "impact", "title": "Impact", "content": "High results can cause the body to fight off infection too aggressively, possibly damaging healthy tissues, or indicate certain blood disorders. Low results can cause a weakened immune system, making the body more susceptible to infections and slower to heal from injuries.\n"}, {"id": "how-to-improve", "title": "How to improve?", "content": "To lower an elevated ASO level, your doctor may prescribe antibiotics to treat the underlying strep infection. Completing the full course of medication is crucial. Rest and staying hydrated can also support your body's recovery and help reduce inflammation. Follow up with your doctor as advised."}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "WBC", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": 635, "report_format_pk": 76862934}, {"index": 22, "test_id": 4030135, "suggestions": [], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "&nbsp", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": null, "report_format_pk": 76862935}, {"index": 29, "test_id": 4030135, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030135, "testName": "Note", "report_name": "COMPLETE BLOOD COUNT ( CBC ) [AUTOMATED]", "dictionary_id": null, "report_format_pk": 76862936}, {"index": 30, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "COMPLETE URINE ANALYSIS", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 683, "report_format_pk": 26066335}, {"index": 3, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "Urine Volume*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 683, "report_format_pk": 75267711}, {"index": 4, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "Urine <PERSON>*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 659, "report_format_pk": 75267712}, {"index": 5, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "Urine Transparency (Appearance)*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 661, "report_format_pk": 75267713}, {"index": 6, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "<PERSON><PERSON>*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 1096, "report_format_pk": 75267714}, {"index": 7, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "Urine Reaction (pH)*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 1162, "report_format_pk": 75267715}, {"index": 8, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "Urine Specific Gravity*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 1091, "report_format_pk": 75267716}, {"index": 9, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "<b>Chemical Examination (Automated Dipstick Method)</b> <small>Urine</small>", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 1021, "report_format_pk": 75267717}, {"index": 10, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "Urine Glucose (sugar)*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 793, "report_format_pk": 75267718}, {"index": 11, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "<PERSON><PERSON> (Albumin)*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 691, "report_format_pk": 75267719}, {"index": 12, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "<PERSON><PERSON> (Acetone)*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 658, "report_format_pk": 75267720}, {"index": 13, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "<PERSON><PERSON>*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 841, "report_format_pk": 75267721}, {"index": 15, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "Urine Bile pigments*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 1258, "report_format_pk": 75267722}, {"index": 17, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "Urine Bile salt", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 674, "report_format_pk": 75267723}, {"index": 16, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "Urine Nitrite*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 669, "report_format_pk": 75267724}, {"index": 18, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "<b>Microscopic Examination</b> <small>Urine</small>", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 1021, "report_format_pk": 75267725}, {"index": 19, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "Pus Cells (WBCs)*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 665, "report_format_pk": 75267726}, {"index": 20, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "Epithelial Cells*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 611, "report_format_pk": 75267727}, {"index": 21, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "Red blood Cells*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 1097, "report_format_pk": 75267728}, {"index": 22, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "Crystals*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 671, "report_format_pk": 75267729}, {"index": 23, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "Cast*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 670, "report_format_pk": 75267730}, {"index": 24, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "Trichomonas Vaginalis*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": null, "report_format_pk": 75267731}, {"index": 25, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "Yeast Cells*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 1095, "report_format_pk": 75267732}, {"index": 26, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "Amorphous deposits*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 672, "report_format_pk": 75267733}, {"index": 27, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "Bacteria*", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": 1094, "report_format_pk": 75267734}, {"index": 29, "test_id": 4030157, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030157, "testName": "Note", "report_name": "COMPLETE URINE ANALYSIS", "dictionary_id": null, "report_format_pk": 75267735}, {"index": 2, "test_id": 4030183, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030183, "testName": "Glyco Hb (HbA1C)", "report_name": "HBA1C", "dictionary_id": 957, "report_format_pk": 75539403}, {"index": 3, "test_id": 4030183, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030183, "testName": "Estimated Average Glucose :", "report_name": "HBA1C", "dictionary_id": 1098, "report_format_pk": 75539404}, {"index": 4, "test_id": 4030183, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030183, "testName": "Interpretations", "report_name": "HBA1C", "dictionary_id": null, "report_format_pk": 75539405}, {"index": 5, "test_id": 4030183, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030183, "testName": "Note", "report_name": "HBA1C", "dictionary_id": null, "report_format_pk": 75539406}, {"index": 4, "test_id": 4030237, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030237, "testName": "Uric Acid*", "report_name": "RENAL PROFILE ( RFT )", "dictionary_id": 367, "report_format_pk": 19457211}, {"index": 10, "test_id": 4030237, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030237, "testName": "Note:", "report_name": "RENAL PROFILE ( RFT )", "dictionary_id": null, "report_format_pk": 69450612}, {"index": 5, "test_id": 4030237, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030237, "testName": "Urea *", "report_name": "RENAL PROFILE ( RFT )", "dictionary_id": 679, "report_format_pk": 76848131}, {"index": 2, "test_id": 4030237, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030237, "testName": "Blood Urea Nitrogen-BUN*", "report_name": "RENAL PROFILE ( RFT )", "dictionary_id": 680, "report_format_pk": 76848132}, {"index": 3, "test_id": 4030237, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030237, "testName": "Creatinine*", "report_name": "RENAL PROFILE ( RFT )", "dictionary_id": 854, "report_format_pk": 76848133}, {"index": 7, "test_id": 4030237, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030237, "testName": "Sodium*", "report_name": "RENAL PROFILE ( RFT )", "dictionary_id": 138, "report_format_pk": 76848134}, {"index": 8, "test_id": 4030237, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030237, "testName": "Potassium*", "report_name": "RENAL PROFILE ( RFT )", "dictionary_id": 850, "report_format_pk": 76848135}, {"index": 9, "test_id": 4030237, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030237, "testName": "Chloride*", "report_name": "RENAL PROFILE ( RFT )", "dictionary_id": 268, "report_format_pk": 76848136}, {"index": 6, "test_id": 4030260, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030260, "testName": " Erythrocyte Sedimentation Rate ", "report_name": "E S R", "dictionary_id": 286, "report_format_pk": 76574374}, {"index": 3, "test_id": 4030260, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030260, "testName": "Interpretation:", "report_name": "E S R", "dictionary_id": null, "report_format_pk": 76574375}, {"index": 5, "test_id": 4030260, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4030260, "testName": "Note", "report_name": "E S R", "dictionary_id": null, "report_format_pk": 76574376}, {"index": 1, "test_id": 4129203, "suggestions": [], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4129203, "testName": "&nbsp", "report_name": "TMT", "dictionary_id": null, "report_format_pk": 22561099}, {"index": 2, "test_id": 4129203, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4129203, "testName": "TEST PERFORMED BY DR. MADHUKAR ON", "report_name": "TMT", "dictionary_id": null, "report_format_pk": 22561100}, {"index": 2, "test_id": 4129203, "suggestions": [{"id": "about-test", "title": "About Test", "content": ""}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": ""}, {"id": "impact", "title": "Impact", "content": ""}, {"id": "how-to-improve", "title": "How to improve?", "content": ""}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 4129203, "testName": "TEST PERFORMED BY DR. MADHUKAR ON", "report_name": "TMT", "dictionary_id": null, "report_format_pk": 22561101}], "use_test_dictionary_relation": false}