/**
 * Update JSON with CSV data
 * @param {Object} jsonData - The JSON data to update
 * @param {Object} csvData - The CSV data
 * @param {Object} testNameMappings - Mappings from JSON test names to CSV test names
 * @param {Array} fieldsToUpdate - Fields to update
 * @param {string} updateMode - Update mode: 'replace', 'add', or 'update'
 * @returns {Object} - The updated JSON data
 */
function updateJsonWithCsvData(jsonData, csvData, testNameMappings, fieldsToUpdate, updateMode) {
    console.log("Updating JSON with CSV data...");
    console.log(`Update mode: ${updateMode}`);
    console.log(`Fields to update: ${fieldsToUpdate.join(', ')}`);
    
    if (!jsonData || !jsonData.summary_items) {
        console.error("Invalid JSON data structure");
        return jsonData;
    }
    
    // Clone the JSON data to avoid modifying the original
    const updatedData = JSON.parse(JSON.stringify(jsonData));
    
    // Process each test item
    updatedData.summary_items.forEach(item => {
        const jsonTestName = item.testName;
        const csvTestName = testNameMappings[jsonTestName];
        
        console.log(`Processing test: ${jsonTestName} -> ${csvTestName}`);
        
        if (!csvTestName || !csvData[csvTestName]) {
            console.log(`No CSV data found for test ${jsonTestName}, skipping`);
            return;
        }
        
        const csvRow = csvData[csvTestName];
        
        // Ensure suggestions array exists
        if (!item.suggestions) {
            item.suggestions = [];
        }
        
        // If replace mode, clear all existing suggestions
        if (updateMode === 'replace') {
            console.log(`Clearing all existing suggestions for ${jsonTestName}`);
            item.suggestions = [];
        }
        
        // Process each field to update
        fieldsToUpdate.forEach(field => {
            // Get content from the CSV
            const content = csvRow[field];
            
            // Skip if no content or empty content
            if (!content || content.trim() === '') {
                console.log(`No content for field ${field}, skipping`);
                return;
            }
            
            // Create clean ID from field name
            const fieldId = field.trim();
            
            console.log(`Processing field: ${fieldId}, content: ${content.substring(0, 30)}...`);
            
            // Check if we should update existing fields or add new ones
            if (updateMode === 'update') {
                // Only update existing fields
                const existingSuggestion = item.suggestions.find(s => s.id === fieldId);
                if (existingSuggestion) {
                    console.log(`Updating existing suggestion: id=${fieldId}`);
                    existingSuggestion.content = content;
                    existingSuggestion.title = fieldId; // Use field ID as title for consistency
                } else {
                    console.log(`Suggestion with id=${fieldId} doesn't exist, skipping (update mode)`);
                }
            } else {
                // Add or replace mode
                const existingSuggestion = item.suggestions.find(s => s.id === fieldId);
                
                if (existingSuggestion) {
                    // Update existing suggestion
                    console.log(`Updating existing suggestion: id=${fieldId}`);
                    existingSuggestion.content = content;
                    existingSuggestion.title = fieldId; // Use field ID as title for consistency
                } else {
                    // Create a new suggestion object
                    console.log(`Adding new suggestion: id=${fieldId}`);
                    const suggestion = {
                        id: fieldId,
                        title: fieldId, // Use field ID as title for consistency
                        content: content
                    };
                    
                    // Add the suggestion to the item
                    item.suggestions.push(suggestion);
                }
            }
        });
        
        console.log(`Test ${jsonTestName} now has ${item.suggestions.length} suggestions`);
    });
    
    return updatedData;
}
