{"summary_items": [{"index": 1, "test_id": 3763974, "suggestions": [{"id": "about-test", "title": "About Test", "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam hendrerit nisi sed sollicitudin pellentesque. Nunc posuere purus rhoncus pulvinar aliquam. Ut aliquet tristique nisl vitae volutpat."}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam hendrerit nisi sed sollicitudin pellentesque. Nunc posuere purus rhoncus pulvinar aliquam. Ut aliquet tristique nisl vitae volutpat."}, {"id": "how-to-improve", "title": "How to improve?", "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam hendrerit nisi sed sollicitudin pellentesque. Nunc posuere purus rhoncus pulvinar aliquam. Ut aliquet tristique nisl vitae volutpat."}, {"id": "myths", "title": "Myths", "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam hendrerit nisi sed sollicitudin pellentesque. Nunc posuere purus rhoncus pulvinar aliquam. Ut aliquet tristique nisl vitae volutpat."}, {"id": "suggested-drugs", "title": "Suggested Drugs", "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam hendrerit nisi sed sollicitudin pellentesque. Nunc posuere purus rhoncus pulvinar aliquam. Ut aliquet tristique nisl vitae volutpat."}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3763974, "testName": "<PERSON><PERSON><PERSON> ( Blank )", "report_name": "<PERSON><PERSON><PERSON>", "dictionary_id": null, "report_format_pk": 17369861}, {"index": 2, "test_id": 3763974, "suggestions": [{"id": "about-test", "title": "About Test", "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam hendrerit nisi sed sollicitudin pellentesque. Nunc posuere purus rhoncus pulvinar aliquam. Ut aliquet tristique nisl vitae volutpat."}, {"id": "cause-of-abnormal-result", "title": "Cause of abnormal result", "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam hendrerit nisi sed sollicitudin pellentesque. Nunc posuere purus rhoncus pulvinar aliquam. Ut aliquet tristique nisl vitae volutpat."}, {"id": "how-to-improve", "title": "How to improve?", "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam hendrerit nisi sed sollicitudin pellentesque. Nunc posuere purus rhoncus pulvinar aliquam. Ut aliquet tristique nisl vitae volutpat."}, {"id": "myths", "title": "Myths", "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam hendrerit nisi sed sollicitudin pellentesque. Nunc posuere purus rhoncus pulvinar aliquam. Ut aliquet tristique nisl vitae volutpat."}, {"id": "suggested-drugs", "title": "Suggested Drugs", "content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam hendrerit nisi sed sollicitudin pellentesque. Nunc posuere purus rhoncus pulvinar aliquam. Ut aliquet tristique nisl vitae volutpat."}], "graph_config": {"dpi": 300, "edge_color": "#1d7761", "fill_color": "#7fccb9", "line_color": "#959fa3", "width_inch": 9, "height_inch": 2, "axis_line_color": "#b2b5b8", "label_font_size": 7, "label_font_color": "#708090", "label_font_weight": "600", "marker_color_inside_range": "#097969", "marker_color_outside_range": "#EE4B2B", "marker_facecolor_inside_range": "#eff9ef", "marker_facecolor_outside_range": "#fcdbd5"}, "impact_ratio": {"text": "{} out of {} people face this issue", "img_path": "/smart_report_files/people_icon.png", "total_count": "10", "affected_count": "8"}, "report_format_id": 3763974, "testName": "<PERSON><PERSON><PERSON>", "report_name": "<PERSON><PERSON><PERSON>", "dictionary_id": null, "report_format_pk": 17397027}], "use_test_dictionary_relation": false}