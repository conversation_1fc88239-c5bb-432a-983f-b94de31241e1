# Updating JSON from CSV Export

This is a simpler alternative to the Google Sheets API approach. You can export your Google Sheet as a CSV file and then use this script to update the JSON.

## Steps

### 1. Export Google Sheet as CSV

1. Open your Google Sheet at https://docs.google.com/spreadsheets/d/1rsODoa2JMNIH4Rxd6jhGW0kWLsU3aN3i_9Q8orQgWBE/edit?gid=2007327401#gid=2007327401
2. Click on "File" > "Download" > "Comma-separated values (.csv)"
3. Save the CSV file to the same directory as the script

### 2. Install Required Packages

```bash
pip install pandas
```

### 3. Run the Script

```bash
python update_json_from_csv.py your_exported_file.csv
```

Replace `your_exported_file.csv` with the name of your exported CSV file.

## CSV Format Requirements

The CSV file should have the following columns:
- "Test Name" - Must match exactly with the "testName" field in the JSON
- "About Test" - Content for the "about-test" section
- "Cause of abnormal result" - Content for the "cause-of-abnormal-result" section
- "Impact" - Content for the "impact" section
- "How to improve" - Content for the "how-to-improve" section

Example CSV format:

```
Test Name,About Test,Cause of abnormal result,Impact,How to improve
VIT B12,Vitamin B12 is essential for...,Low levels can be caused by...,Deficiency can lead to...,Increase consumption of...
TSH,Thyroid Stimulating Hormone...,Elevated levels may indicate...,Thyroid issues can affect...,Consult with your doctor...
```

## Troubleshooting

- If the script can't find your CSV file, make sure you're providing the correct path
- If no fields are being updated, check that the test names in your CSV exactly match the "testName" values in the JSON file
- If you get errors about missing columns, make sure your CSV has all the required column headers
