#!/usr/bin/env python3
import json
import pandas as pd
import sys
import os

def update_json_with_csv_data(json_file, csv_file):
    """
    Update the JSON file with data from a CSV file.
    """
    try:
        # Check if CSV file exists
        if not os.path.exists(csv_file):
            print(f"Error: CSV file '{csv_file}' not found.")
            return False
        
        # Load the CSV file
        try:
            sheet_data = pd.read_csv(csv_file)
        except Exception as e:
            print(f"Error reading CSV file: {e}")
            return False
        
        # Load the JSON file
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
        except Exception as e:
            print(f"Error reading JSON file: {e}")
            return False
        
        # Create a dictionary to map test names to their data from the sheet
        test_data_map = {}
        for _, row in sheet_data.iterrows():
            test_name = row.get('Test Name', '').strip()
            if test_name:
                test_data_map[test_name] = {
                    'about': row.get('About Test', ''),
                    'cause': row.get('Cause of abnormal result', ''),
                    'impact': row.get('Impact', ''),
                    'improve': row.get('How to improve', '')
                }
        
        # Update the JSON data
        updated_count = 0
        for item in data.get('summary_items', []):
            test_name = item.get('testName', '')
            if test_name in test_data_map:
                sheet_info = test_data_map[test_name]
                
                # Update the suggestions
                for suggestion in item.get('suggestions', []):
                    suggestion_id = suggestion.get('id', '')
                    
                    if suggestion_id == 'about-test':
                        suggestion['content'] = sheet_info['about']
                        updated_count += 1
                    elif suggestion_id == 'cause-of-abnormal-result':
                        suggestion['content'] = sheet_info['cause']
                        updated_count += 1
                    elif suggestion_id == 'impact':
                        suggestion['content'] = sheet_info['impact']
                        updated_count += 1
                    elif suggestion_id == 'how-to-improve':
                        suggestion['content'] = sheet_info['improve']
                        updated_count += 1
        
        # Save the updated JSON
        with open(json_file, 'w') as f:
            json.dump(data, f, indent=2)
        
        print(f"Updated {updated_count} fields in the JSON file.")
        return True
    
    except Exception as e:
        print(f"Error updating JSON: {e}")
        return False

def main():
    # Check command line arguments
    if len(sys.argv) < 2:
        print("Usage: python update_json_from_csv.py <csv_file>")
        print("Example: python update_json_from_csv.py test_data.csv")
        return
    
    csv_file = sys.argv[1]
    json_file = 'TruScan_EXECUTIVE HEALTH PACKAGE.json'
    
    print(f"Updating JSON from CSV file: {csv_file}")
    success = update_json_with_csv_data(json_file, csv_file)
    
    if success:
        print("Successfully updated the JSON file.")
    else:
        print("Failed to update the JSON file.")

if __name__ == "__main__":
    main()
