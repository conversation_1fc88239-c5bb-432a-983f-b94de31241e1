# JSON Updater Tool - Instructions

This tool helps you update your JSON file with data from a CSV file exported from Google Sheets.

## How to Use

1. **Export Your Google Sheet as CSV**
   - Open your Google Sheet
   - Go to File > Download > Comma-separated values (.csv)
   - Save the CSV file to your computer

2. **Open the HTML Tool**
   - Double-click the `simple_json_updater.html` file to open it in your web browser
   - No internet connection is required - the tool works completely offline
   - All processing happens locally in your browser for maximum privacy and security

3. **Select Your Files**
   - Click "Choose File" in Step 1 to select your CSV file
   - Click "Choose File" in Step 2 to select your JSON file

4. **Upload Mapping File (Optional)**
   - If test names in your CSV don't match those in your JSON, you'll need to create mappings
   - First select your JSON and CSV files in Steps 1 and 2
   - Click "Download CSV Template" to get a CSV template with auto-generated mappings
   - The tool will generate two CSV files:
     - A mapping template with automatically suggested mappings based on your files
     - A reference file with all test names from your JSON and CSV
   - Special test names (comments, notes, interpretations) are marked with `[EMPTY]`
   - The tool uses intelligent matching to suggest mappings:
     - Abbreviation expansion (e.g., "VIT B12" → "Vitamin B12")
     - Substring matching (e.g., "Hemoglobin" → "Hemoglobin Test")
     - Word similarity matching
   - Review the suggested mappings and make any necessary changes in a spreadsheet program
   - You can mark any test name with `[EMPTY]` to ensure it has empty suggestion arrays
   - Save the template as CSV and upload it using the file selector
   - Click "Upload Mapping File" to process your mappings

5. **Update & Download JSON**
   - Click the "Update JSON" button
   - The tool will process your files and show progress with a progress bar
   - When complete, click "Download Updated JSON" to save the updated file

## Special Features

### Column Detection

The tool automatically looks for these columns in your CSV:

- **Test Name Column**: "Dic. Name | Checked 255/1062" or first column
- **About Test**: "About Parameter", "About Test", "About", etc.
- **Cause**: "Cause of abnormal result", "Cause", etc.
- **Impact**: "Impact of abnormal result", "Impact", etc.
- **How to improve**: "How to improve", "Improve", etc.

### Special Test Name Handling

The tool automatically identifies special test names that should have empty suggestion arrays:
- Comments/Comment (with or without colon)
- Notes/Note (with or without colon)
- Interpretation/Interpretations
- &nbsp;
- "-" (dash)

### Using the [EMPTY] Marker

In the mapping template, you'll see some test names marked with `[EMPTY]` in the CSV Test Name column. This means:

1. These test names will have completely empty suggestion arrays (`[]`) in the final JSON
2. No content from the CSV will be applied to these test names
3. This is useful for comment fields, notes, interpretations, etc. that should not have any content

You can also manually mark any test name with `[EMPTY]` if you want it to have empty suggestions.

### Important Note About Mappings

**Only test names that have mappings in Step 3 will get content from the CSV file.** All other test names will have empty suggestion arrays ([]) in the final JSON. This means:

1. If you want a test name to have content from the CSV, you must include it in your mapping file
2. If you want a test name to have empty suggestions, either mark it with `[EMPTY]` or don't include it in your mapping file
3. Any test name not included in your mapping file will automatically have empty suggestions ([])

## Troubleshooting

If you encounter any issues:

1. **Check your CSV format**: Make sure it has the correct column headers
2. **Check for matching test names**: The test names in your CSV must match those in the JSON
3. **Use a mapping file**: If test names don't match exactly, use the mapping feature
4. **Look at the output area**: It shows detailed information about what's happening
