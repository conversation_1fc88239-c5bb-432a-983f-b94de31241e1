// Show which test names are not in the CSV
const csvTestNames = new Set();
for (const testName in testData) {
    csvTestNames.add(testName);
}

const notInCsv = [];
for (const item of updatedJsonData.summary_items || []) {
    const testName = item.testName || '';
    if (testName && !csvTestNames.has(testName)) {
        notInCsv.push(testName);
    }
}

if (notInCsv.length > 0) {
    log('\n==== TEST NAMES NOT IN CSV ====');
    log(`Found ${notInCsv.length} test names in JSON that are not in the CSV:`);
    for (let i = 0; i < Math.min(notInCsv.length, 20); i++) {
        log(`- ${notInCsv[i]}`);
    }
    if (notInCsv.length > 20) {
        log(`... and ${notInCsv.length - 20} more.`);
    }
}
