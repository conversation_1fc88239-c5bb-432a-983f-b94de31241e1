#!/usr/bin/env python3
import json
import pandas as pd
import sys
import os

def update_json_from_csv(json_file, csv_file, debug=False):
    """
    A simple script to update the JSON file with data from a CSV file.
    Matches test names from JSON with test names in the CSV and updates the content.
    Specifically looks for test names in 'Dic. Name | Checked 255/1062' or column 'A'.
    """
    try:
        # Check if files exist
        if not os.path.exists(json_file):
            print(f"Error: JSON file '{json_file}' not found.")
            return False

        if not os.path.exists(csv_file):
            print(f"Error: CSV file '{csv_file}' not found.")
            return False

        # Load the CSV file
        print(f"Loading CSV data from {csv_file}...")
        sheet_data = pd.read_csv(csv_file)
        print(f"Found {len(sheet_data)} rows in the CSV file.")

        if debug:
            print("\nCSV columns found:")
            for col in sheet_data.columns:
                print(f"  - {col}")
            print()

        # Identify the column to use for test names
        test_name_column = None
        content_columns = {}

        # Check for the dictionary name column
        if 'Dic. Name | Checked 255/1062' in sheet_data.columns:
            test_name_column = 'Dic. Name | Checked 255/1062'
            print(f"Using '{test_name_column}' column for test names")
        # If not found, try the first column (A)
        elif len(sheet_data.columns) > 0:
            test_name_column = sheet_data.columns[0]  # First column (A)
            print(f"Using first column '{test_name_column}' for test names")
        else:
            print("Error: Could not identify a column for test names")
            return False

        # Find content columns - try different possible names
        possible_about_columns = ['About Parameter', 'About Test', 'About', 'about', 'About test']
        possible_cause_columns = ['Cause of abnormal result', 'Cause', 'cause', 'Abnormal result']
        possible_impact_columns = ['Impact of abnormal result', 'Impact', 'impact', 'Effects']
        possible_improve_columns = ['How to improve', 'Improve', 'improve', 'How to improve?']

        # Find the about column
        for col in possible_about_columns:
            if col in sheet_data.columns:
                content_columns['about-test'] = col
                print(f"Using '{col}' column for About Test content")
                break

        # Find the cause column
        for col in possible_cause_columns:
            if col in sheet_data.columns:
                content_columns['cause-of-abnormal-result'] = col
                print(f"Using '{col}' column for Cause content")
                break

        # Find the impact column
        for col in possible_impact_columns:
            if col in sheet_data.columns:
                content_columns['impact'] = col
                print(f"Using '{col}' column for Impact content")
                break

        # Find the improve column
        for col in possible_improve_columns:
            if col in sheet_data.columns:
                content_columns['how-to-improve'] = col
                print(f"Using '{col}' column for How to improve content")
                break

        # Create a dictionary from the CSV data
        test_data = {}
        for _, row in sheet_data.iterrows():
            # Get the test name from the CSV
            test_name = str(row.get(test_name_column, '')).strip()
            if not test_name or pd.isna(test_name):
                continue

            # Store the data for this test
            test_data[test_name] = {}

            # Add content for each field if the column exists
            for field_id, column_name in content_columns.items():
                content = row.get(column_name, '')
                if not pd.isna(content):
                    test_data[test_name][field_id] = str(content)
                else:
                    test_data[test_name][field_id] = ''

        print(f"Processed {len(test_data)} tests from the CSV file.")

        if debug and test_data:
            print("\nSample of test data extracted from CSV:")
            sample_count = 0
            for test_name, data in test_data.items():
                if sample_count < 3:  # Show up to 3 samples
                    print(f"\nTest: {test_name}")
                    for field, content in data.items():
                        print(f"  {field}: {content[:50]}..." if len(content) > 50 else f"  {field}: {content}")
                    sample_count += 1
                else:
                    break
            print()

        # Load the JSON file
        print(f"Loading JSON data from {json_file}...")
        with open(json_file, 'r') as f:
            json_data = json.load(f)

        # Update the JSON data
        updated_count = 0
        test_count = 0
        matched_tests = []

        for item in json_data.get('summary_items', []):
            test_name = item.get('testName', '')
            test_count += 1

            # Check if this test name exists in our CSV data
            if test_name in test_data:
                print(f"Found match for test: {test_name}")
                matched_tests.append(test_name)
                csv_data = test_data[test_name]

                # Update each suggestion
                for suggestion in item.get('suggestions', []):
                    suggestion_id = suggestion.get('id', '')

                    if suggestion_id in csv_data and csv_data[suggestion_id]:
                        suggestion['content'] = csv_data[suggestion_id]
                        updated_count += 1

        # Print unmatched tests
        print(f"\nMatched {len(matched_tests)} out of {test_count} tests in the JSON file.")
        if len(matched_tests) < test_count:
            print("\nThe following tests in the JSON file did not have matches in the CSV:")
            for item in json_data.get('summary_items', []):
                test_name = item.get('testName', '')
                if test_name and test_name not in matched_tests:
                    print(f"  - {test_name}")

        # Save the updated JSON
        print(f"\nSaving updated JSON to {json_file}...")
        with open(json_file, 'w') as f:
            json.dump(json_data, f, indent=2)

        print(f"Updated {updated_count} content fields.")
        return True

    except Exception as e:
        print(f"Error: {e}")
        return False

def main():
    # Default CSV file name
    default_csv = "Checked Smart Report Parameter Db - Db.csv"
    debug_mode = False

    # Check for debug flag
    if len(sys.argv) > 1 and sys.argv[1] == "--debug":
        debug_mode = True
        print("Running in debug mode...")
        if len(sys.argv) > 2:
            csv_file = sys.argv[2]
        elif os.path.exists(default_csv):
            csv_file = default_csv
        else:
            print(f"Error: Default CSV file '{default_csv}' not found.")
            print("Please provide a CSV file name:")
            print(f"Usage: python simple_update_json.py [--debug] <csv_file>")
            return
    else:
        # Normal mode
        if os.path.exists(default_csv):
            csv_file = default_csv
        elif len(sys.argv) > 1:
            # Use command line argument if provided
            csv_file = sys.argv[1]
        else:
            print(f"Error: Default CSV file '{default_csv}' not found.")
            print("Please provide a CSV file name:")
            print(f"Usage: python simple_update_json.py [--debug] <csv_file>")
            print(f"Example: python simple_update_json.py {default_csv}")
            return

    json_file = 'TruScan_EXECUTIVE HEALTH PACKAGE.json'

    print("Starting JSON update process...")
    success = update_json_from_csv(json_file, csv_file, debug=debug_mode)

    if success:
        print("Successfully updated the JSON file!")
    else:
        print("Failed to update the JSON file.")

if __name__ == "__main__":
    main()
