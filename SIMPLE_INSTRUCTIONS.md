# Simple JSON Update Instructions

This is a straightforward process to update your JSON file with data from your Google Sheet.

## Step 1: Export Your Google Sheet as CSV

1. Open your Google Sheet at: https://docs.google.com/spreadsheets/d/1rsODoa2JMNIH4Rxd6jhGW0kWLsU3aN3i_9Q8orQgWBE/edit?gid=2007327401#gid=2007327401
2. Click on "File" > "Download" > "Comma-separated values (.csv)"
3. Save the CSV file to the same folder as the script

## Step 2: CSV Column Requirements

The script will automatically look for these columns:

### Test Name Column (one of these):
- "Dic. Name | Checked 255/1062"
- Or the first column (A) if the above is not found

### Content Columns (will look for these specific columns):
- About Test: "About Parameter" (preferred), or "About Test", "About", etc.
- Cause: "Cause of abnormal result" (preferred), or "Cause", etc.
- Impact: "Impact of abnormal result" (preferred), or "Impact", etc.
- How to improve: "How to improve" (preferred), or "Improve", etc.

## Step 3: Run the Script

The script will automatically look for a file named "Checked Smart Report Parameter Db - Db.csv" in the same folder. Just run:

```bash
python3 simple_update_json.py
```

If you want to use a different CSV file, you can specify it:

```bash
python3 simple_update_json.py your_exported_file.csv
```

### Troubleshooting with Debug Mode

If you're having issues with the script, you can run it in debug mode to see more information:

```bash
python3 simple_update_json.py --debug
```

Debug mode will show:
- All columns found in your CSV file
- Sample data extracted from the CSV
- More detailed matching information

## Step 4: Check the Results

The script will tell you:
- How many tests it found in the CSV
- How many tests it processed in the JSON
- How many content fields it updated

## Troubleshooting

If the script doesn't update all the fields you expect:

1. Run the list_test_names.py script to see all test names in the JSON:
   ```bash
   python3 list_test_names.py
   ```

2. Make sure the "Test Name" values in your CSV exactly match the test names from the JSON file

3. Check that your CSV has all the required column headers spelled exactly as shown above
