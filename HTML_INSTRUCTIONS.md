# JSON Updater Tool - HTML Interface

This is a simple web-based tool that allows you to update your JSON file with data from a CSV file without needing to use the command line or install any software.

## How to Use the HTML Interface

1. **Download the Files**
   - Save the `json_updater.html` file to your computer

2. **Open the HTML File**
   - Double-click the `json_updater.html` file to open it in your web browser
   - It will work in any modern browser (Chrome, Firefox, Safari, Edge)
   - No internet connection is required - everything runs locally in your browser

3. **Select Your CSV File**
   - Click "Choose File" in Step 1 to select your CSV file exported from Google Sheets
   - The file should have columns for test names and content (About Parameter, Cause of abnormal result, etc.)

4. **Select Your JSON File**
   - Click "Choose File" in Step 2 to select your JSON file that needs to be updated
   - This should be the `TruScan_EXECUTIVE HEALTH PACKAGE.json` file

5. **Upload Mapping File (Optional)**
   - If test names in your CSV don't exactly match those in your JSON, upload an Excel file with your mappings
   - Your Excel file should have two columns: JSON Test Name and CSV Test Name
   - Example: First column "VIT B12", second column "Vitamin B12"
   - Click "Upload Mapping File" to process your mappings

6. **Update & Download JSON**
   - Click the "Update JSON" button
   - The tool will process your files and show progress with a progress bar
   - When complete, a "Download Updated JSON" button will appear
   - Click the "Download Updated JSON" button to save the updated JSON file
   - The file will be saved as `updated_[original-filename].json`

## Advantages of the HTML Interface

- **No Installation Required**: Works directly in your web browser
- **User-Friendly**: Simple point-and-click interface
- **Excel Mapping**: Upload an Excel file with your custom mappings between JSON and CSV test names
- **Visual Progress**: Progress bars show you exactly what's happening during processing
- **Detailed Feedback**: Shows exactly what's happening during the update process
- **Works Offline**: No internet connection needed
- **Cross-Platform**: Works on Windows, Mac, and Linux

## Column Mapping

The tool will automatically look for these columns in your CSV:

| JSON Field | CSV Column Names (in order of preference) |
|------------|------------------------------------------|
| Test Name | "Dic. Name \| Checked 255/1062" or first column |
| About Test | "About Parameter", "About Test", "About", etc. |
| Cause of abnormal result | "Cause of abnormal result", "Cause", etc. |
| Impact | "Impact of abnormal result", "Impact", etc. |
| How to improve | "How to improve", "Improve", etc. |

## Special Features

### Handling Special Test Names

The tool automatically detects and fixes test items with empty suggestion arrays (`"suggestions": []`). When it finds these special cases, it adds the standard suggestion structure so they can be populated with content from your CSV.

#### Example

**Before:**
```json
{
  "testName": "Comments:",
  "suggestions": []  // Empty array will be fixed
}
```

**After:**
```json
{
  "testName": "Comments:",
  "suggestions": [  // Standard structure added
    {
      "id": "about-test",
      "title": "About Test",
      "content": ""
    },
    {
      "id": "cause-of-abnormal-result",
      "title": "Cause of abnormal result",
      "content": ""
    },
    {
      "id": "impact",
      "title": "Impact",
      "content": ""
    },
    {
      "id": "how-to-improve",
      "title": "How to improve?",
      "content": ""
    }
  ]
}
```

#### Supported Test Names

**Exact matches (case insensitive):**
- "&nbsp"
- "Comments" or "Comments:"
- "Comment" or "Comment:"
- "Note" or "Note:"
- "Notes" or "Notes:"
- "Interpretation" or "Interpretation:"
- "Interpretations" or "Interpretations:"
- "INTERPRETATION"
- "-" (dash)

**Partial matches (case insensitive):**
Any test name containing:
- "comment"
- "note"
- "interpretation"
- "&nbsp"

## Troubleshooting

If you encounter any issues:

1. **Check your CSV format**: Make sure it has the correct column headers
2. **Check for matching test names**: The test names in your CSV must match those in the JSON
3. **Look at the output area**: It will show detailed information about what's happening
4. **Try a different browser**: If you have issues with one browser, try another
